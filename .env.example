# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Flask Configuration
SECRET_KEY=your_secret_key_here
FLASK_ENV=development

# Transcription Configuration
# Options: 'openai_api', 'local_whisper', 'faster_whisper'
TRANSCRIPTION_METHOD=openai_api

# Local Whisper Model (if using local methods)
# Options: 'tiny', 'base', 'small', 'medium', 'large', 'turbo'
WHISPER_MODEL=base

# File Upload Configuration
MAX_FILE_SIZE_MB=200

# Audio Processing Configuration
CHUNK_SIZE_SECONDS=30
CHUNK_OVERLAP_SECONDS=2
ENABLE_TIMESTAMPS=true
ENABLE_WORD_TIMESTAMPS=false

# Performance Configuration
PARALLEL_PROCESSING=true
MAX_WORKERS=4
ENABLE_CACHING=true
CACHE_DURATION_HOURS=24

# Background Processing (for large files)
ENABLE_BACKGROUND_PROCESSING=true
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Model Selection
AUTO_MODEL_SELECTION=true
SMALL_FILE_THRESHOLD_MB=50
LARGE_FILE_MODEL=base
SMALL_FILE_MODEL=small
